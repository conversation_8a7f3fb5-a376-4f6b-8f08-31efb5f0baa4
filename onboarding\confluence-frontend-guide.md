Build

The npm run build script includes running the linters, unit tests and compiling the application. This is what the build pipeline executes, and you need run it locally before submitting a PR to make sure every step passes with no errors or warnings.

Components

Use standalone components.

Prefer importing standalone dependencies over modules (i.e. NgSelectComponent instead of NgSelectModule)

Use Signals. 

Static properties should be avoided.

Use toSignals, or at the very least, mapped observables with async pipe, instead of assigning local property inside subscriptions.

Consider computed and linkedSignals for dependent values.

Don’t subscribe unless absolutely necessary.

If subscribing, use takeUntilDestroyed to ensure resource clean up.

Use OnPush change strategy in new components. 

Migrate existing components with Default change strategy when working on them, unless under time constraints or high complexity.

Use the right access modifiers for properties and methods

private when used inside the component code.

protected when used in the template.

public for inputs and lifecycle hooks. Do not expose public methods for testing purposes, use fixture instead.

The application was originally designed around NgModules. Use the folder organization that modules provided, treating “modules” as “feature”.

Use “module” folder for new feature.

Add router and lazy import route in parent router.

/components: Angular Components

/services: featurename.service.ts extending ApiService to communicate with the API. Routes should be follow the RESTful pattern.

/requests: Classes used in the api service to submit requests. These should map to the C# models in the corresponding endpoint, and it should mostly match the Reactive Form where it is consumed.

Example:

modules/users/requests/create-user.request.ts


export class CreateUserRequest {
    public email: string;
    public roleId: number;
    public organizationId: number;
    public firstName: string;
    public lastName: string;
}


used in modules/users/components/create-user.component.ts


submitForm() {
        this.isSubmitted = true;
        if (this.form.valid) {
            this.userService.createUser(this.form.getRawValue() as CreateUserRequest)
                .pipe(takeUntil(this.destroyed$))
                .subscribe(res => {
                    this.notificationService.notify(res.message);
                    this.activeModal.close(res);
                });
        }
    }




/models: Classes used to match service responses. These should map to the C# models in the corresponding endpoint. Also, classes used internally by the components should go here. 



Routing

Do

Use lazy loaded components and routes.

Use guards.

Tests

Do

Cover your implementation with unit tests where it makes sense.

Use fixture instead of accessing the component API directly, except for specific helper functions that might need logic testing.

Use jasmine marbles to test observables.

Always TestBed to setup your tests.

Use custom attribute data-testid to identify elements that cannot be easily identified inside unit tests.

Make sure your tests pass before submitting your PR (check out the npm scripts in package.json for test run options).

There are two scripts included: 

npm run test Runs tests on a full instance of Chrome, to allow debugging. This also generates a coverage report accessible on ClientApp/coverage/{browser}/index.html.

npm run test-ci Runs tests on a headless Chrome instance, and it is what the build pipeline executes. It is a good idea to verify this before submitting the PR. 

Don’t

Ignore unit testing. Your PR won’t be completed until this is at least discussed.

Consider

Doing fixture testing including DOM interaction.

Angular Templates

Try to avoid function calls for computing values in Angular template expressions.

The Angular change detection mechanism needs to evaluate template functions to determine whether elements need to be re rendered. Because Angular cannot predict whether the return value of a function has changed, it needs to execute the function every time change detection runs. So if change detection runs 300 times, the function is called 300 times, even if its return value never changes. Depending on the logic inside the function, running a function hundreds of times can become a serious performance issue.

In order to avoid this performance issue, try any of these solutions:

When possible, store the returned value of the function in a field and use that field in the Angular template instead of a direct call to the function. (A)

Use pure pipes. Shared pipes are located in the Shared Module, and it must be used for consistency when displaying data.

Use signals.

When working with BaseListComponent<TEntity> use the toItem() method to type row variables on template (B)

Below is an example of solution (A)

Don’t make a call to getIconClass function:

@Component({
    selector: 'app-table-action',
    template: '<i title="{{ title }}" [class]="getIconClass()"></i>'
})
export class TableActionComponent {
  public title = 'MyTitle';
  
  public getIconClass(): string {
      return this.icon + (this.show ? (this.enabled ? ' status-enabled' : ' status-disabled') : ' status-hidden');
  }
}

Instead, try something like this when possible:

@Component({
    selector: 'app-table-action',
    template: '<i title="{{ title }}" [class]="iconClass"></i>'
})
export class TableActionComponent implements OnInit{
    public title = 'MyTitle';
    public iconClass = '';

    ngOnInit(): void {
      this.iconClass = this.icon + (this.show ? (this.enabled ? ' status-enabled' : ' status-disabled') : ' status-hidden');
    }
}

Below is an example of solution (B)

row is any type here:

<ng-template #someTableTemplate let-row="row">
    {{ row.someProperty }}
</ng-template>

But using the toItem() method the row variable now gets the table entity type, providing intellisense and also preventing the build to compile even when the entity doesn’t have the someProperty property anymore:

<ng-template #someTableTemplate let-row="row">
    <ng-container *ngIf="toItem(row);let row">
        {{ row.someProperty }}
    </ng-container>
</ng-template>

Styling

Do

Use bootstrap classes.

Use bootstrap flexbox utilities.

Extend and override bootstrap variables in custom-variables.scss. This is the preferred way for customizing the default theme.

If the style you need is not included in Bootstrap, before creating a new one, check the shared styles to see if there is already a style you can reuse. Shared styles are located in several files in the styles/app folder.

Don’t

Use [style] directly on the html element. Always use classes.

Forms

Do 

Always

Use Reactive Forms API to interact with Forms and HTML elements:

Validate inputs using reactive forms validators.

Handle input changes using FormControl valueChanges. 

Toggle input state using the enable / disable functionality of the FormControl.

Use Typed Forms

Construct the form with all the know values and state, instead of modifying it after creation.

Don’t

Interact with HTML elements directly using HTML events or [(ngModel)].

Use Angular template forms.

Use ng-select component for drop-downs

We use ng-select instead of standard HTML select elements. When the expected number of items in the drop-down is high, use the ng-select typeahead option. See an example in create-user-component.ts

Always bind the ng-select to a formControl, and handle changes subscribing to the FormControl.valueChanges observable. The only exception to this rule is when using typeahead, clearing the input with Backspace doesn’t trigger valueChanges. In these cases, use the (clear) event hooked up to a function to reset the input observable.

Api Services

Each module should have a service class to interact with the API, as described in the https://ippathways.atlassian.net/wiki/spaces/MYADAPTIVE/pages/1002209281/UI+Best+practices+and+Patterns#Modules section.

These services should be root injected singletons.

The service must extend the ApiService class, which includes methods to interact with the HttpClient, including handling errors and notifications.

ApiService includes client time zone information with each request. Mark endpoints that require this information with the UIClientTimeZoneRequired attribute. This will ensure that the header is present with a valid TimeZoneId.

Do

User proper HTTP verbs and leverage the existing methods in the ApiService class. 

Read the Microsoft best practices guide for cloud APIs: https://learn.microsoft.com/en-us/azure/architecture/best-practices/api-design 

Use RESTful routes.

GET requests: Send request objects using querystring parameters and receive ApiDataResult<T> objects when retrieving data without filtering, sorting and pagination, or ApiDatasetResult<T> when retrieving data with filtering, sorting and pagination.

POST requests: Always include request body matching exactly the API endpoint request argument. Return 201 ApiResult with operation result message. Also use in cases where an action is needed that does not make sense to convert into a resource (i.e: POST /organizations/{organizationId}/approve)

PUT requests: Always send entity id to be updated, and include request body matching exactly the API endpoint request argument. Return ApiResult with operation result message.

DELETE request: Always send entity id to be updated. Return ApiResult with operation result message.

State Management

Local state management

For components that require local state management, use @ngrx/signals.

A good use case for this is when a set of components in the same component tree node need to share state, and this state is not relevant to the global application, and it does not need to persist after the components are destroyed.