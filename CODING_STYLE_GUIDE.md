# MyAdaptiveCloud Coding Style Guide

## Table of Contents
- [Project Overview](#project-overview)
- [Backend C# Conventions](#backend-c-conventions)
- [Frontend Angular Conventions](#frontend-angular-conventions)
- [Data Layer Conventions](#data-layer-conventions)
- [Database Schema Conventions](#database-schema-conventions)
- [API Design Patterns](#api-design-patterns)
- [Error Handling and Logging](#error-handling-and-logging)
- [Code Templates](#code-templates)
- [Anti-Patterns to Avoid](#anti-patterns-to-avoid)

## Project Overview

MyAdaptiveCloud is a multi-layered ASP.NET Core application with Angular frontend, following clean architecture principles with clear separation of concerns.

### Technology Stack
- **Backend**: ASP.NET Core 8, Entity Framework Core, AutoMapper
- **Frontend**: Angular 20, TypeScript, RxJS, NgRx Signals
- **Database**: MariaDB 10.11+ with Liquibase migrations
- **Testing**: x<PERSON><PERSON><PERSON> (Backend), <PERSON> (Frontend), <PERSON><PERSON> (E2E)

### Project Structure
```
src/
├── MyAdaptiveCloud/              # Main web application & API
│   ├── ClientApp/                # Angular frontend
│   ├── Controllers/              # API controllers
│   ├── Authorization/            # Auth filters & policies
│   ├── Middleware/               # Custom middleware
│   ├── ViewModel/                # API response models
│   └── Requests/                 # API request models
├── MyAdaptiveCloud.Core/         # Core business logic & domain models
├── MyAdaptiveCloud.Data/         # Data access layer & EF models
├── MyAdaptiveCloud.Services/     # Business services & external APIs
└── MyAdaptiveCloud.WorkerServices/ # Background services
```

## Backend C# Conventions

### Naming Conventions

#### Classes and Interfaces
```csharp
// Controllers
public class ServiceController : AuthenticatedControllerBase { }
public class DevicesController : AuthenticatedControllerBase { }

// Services
public interface IServicesService { }
public class ServicesService : IServicesService { }

// Repositories
public interface IOrganizationRepository { }
public class OrganizationRepository : IOrganizationRepository { }

// DTOs
public class ServiceDTO { }
public class AgentDTO { }

// Request Models
public class CreateServiceRequest { }
public class EditServiceRequest { }

// View Models
public class ServiceTicketViewModel { }
public class ACMappingViewModel { }
```

#### Properties and Fields
```csharp
// Public properties - PascalCase
public int OrganizationId { get; set; }
public string CompanyShortName { get; set; }
public DateTime CreatedDate { get; set; }

// Private fields - camelCase with underscore prefix
private readonly IServicesService _serviceService;
private readonly IMapper _mapper;
private readonly ILogger<ServiceController> _logger;
```

### Controller Patterns

#### Base Controller Structure
```csharp
public class ServiceController : AuthenticatedControllerBase
{
    private readonly IServicesService _serviceService;

    public ServiceController(
        IIdentityService identityService,
        IMapper mapper,
        IServicesService serviceService)
        : base(identityService, mapper)
    {
        _serviceService = serviceService;
    }

    [OrgAuthorize(Perms.ViewServices, Perms.ManageServices)]
    [HttpGet("{organizationId:int}/list")]
    public async Task<ActionResult<ApiDataSetResult<List<ServiceDTO>>>> GetServices(
        [FromRoute] int organizationId)
    {
        var result = await _serviceService.GetList(organizationId);
        return new ApiDataSetResult<List<ServiceDTO>>
        {
            Data = result,
            TotalCount = result.Count,
        };
    }
}
```

#### Authorization Patterns
All endpoints must have authorization filters following these guidelines:

```csharp
// Preferred: Authorize against organization linked to entity
// Pass ONLY entity ID, resolve organization internally
[EscalationChainAuthorize]
public async Task<ActionResult> GetEscalationChain([FromRoute] int id) { }

// Fallback: Use organization ID from user context
[OrgAuthorize(Perms.ManageServices)]
public async Task<ActionResult> GetServices([FromRoute] int organizationId) { }

// Agent-based authorization
[AgentAuthorize(Perms.ManageDevices, Name = "agentId")]

// Role-based authorization
[RootOrgAuthorize(Perms.ManageSalesResourceHub)]

// Feature flag authorization
[FeatureFlagsAuthorize(FeatureFlagConstants.FeatureFlagSalesResourceHub)]
```

#### Controller Best Practices
```csharp
// ✅ Do: Keep controllers lean, use services for logic
[HttpGet("{organizationId:int}/list")]
public async Task<ActionResult<ApiDataSetResult<List<ServiceDTO>>>> GetServices(
    [FromRoute] int organizationId)
{
    var result = await _serviceService.GetList(organizationId);
    return new ApiDataSetResult<List<ServiceDTO>>
    {
        Data = result,
        TotalCount = result.Count,
    };
}

// ✅ Do: Return NotFound for specific entities
[HttpGet("{id:int}")]
public async Task<ActionResult<ApiDataResult<ServiceDTO>>> GetService([FromRoute] int id)
{
    var result = await _serviceService.GetById(id); // Throws NotFoundException if not found
    return new ApiDataResult<ServiceDTO> { Data = result };
}

// ❌ Don't: Use DbContext directly in controllers
[HttpGet]
public async Task<List<Service>> GetServices()
{
    return await _context.Service.ToListAsync(); // Direct data access
}

// ❌ Don't: Pass user ID from UI
[HttpPost]
public async Task<ActionResult> CreateService([FromBody] CreateServiceRequest request, int userId)
{
    // Always get user ID from claims using IIdentityService
}
```

### Service Layer Patterns

#### Service Interface
```csharp
public interface IServicesService
{
    Task<List<ServiceDTO>> GetList(int organizationId);
    Task<ServiceDTO> GetById(int serviceId);
    Task CreateService(int organizationId, CreateServiceRequest request);
    Task EditService(int serviceId, EditServiceRequest request);
    Task DeleteService(int serviceId);
}
```

#### Service Implementation
```csharp
public class ServicesService : IServicesService
{
    private readonly IServiceRepository _serviceRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<ServicesService> _logger;

    public ServicesService(
        IServiceRepository serviceRepository,
        IMapper mapper,
        ILogger<ServicesService> logger)
    {
        _serviceRepository = serviceRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<ServiceDTO>> GetList(int organizationId)
    {
        var services = await _serviceRepository.GetByOrganizationAsync(organizationId);
        return _mapper.Map<List<ServiceDTO>>(services);
    }

    // Exception handling for NotFound/BadRequest scenarios
    public async Task<ServiceDTO> GetById(int serviceId)
    {
        var service = await _serviceRepository.GetByIdAsync(serviceId);
        if (service == null)
        {
            throw new NotFoundException(serviceId, "Service not found");
        }
        return _mapper.Map<ServiceDTO>(service);
    }

    public async Task UpdateService(int serviceId, EditServiceRequest request)
    {
        var service = await _serviceRepository.GetByIdAsync(serviceId);
        if (service == null)
        {
            throw new NotFoundException(serviceId, "Service not found");
        }

        if (!IsValidUpdate(request))
        {
            throw new BadRequestException("Invalid service update request");
        }

        _mapper.Map(request, service);
        await _serviceRepository.UpdateAsync(service);
    }
}
```

#### DTO Usage Guidelines
```csharp
// ✅ Always use DTOs for API data transfer
public async Task<List<ServiceDTO>> GetServices(int organizationId)
{
    return await _context.Service
        .Where(s => s.OrganizationId == organizationId)
        .Select(s => new ServiceDTO
        {
            Id = s.Id,
            Name = s.Name,
            Description = s.Description,
            IsActive = s.IsActive
        })
        .ToListAsync();
}

// ❌ Never return entities directly to API
public async Task<List<Service>> GetServices(int organizationId)
{
    return await _context.Service
        .Where(s => s.OrganizationId == organizationId)
        .ToListAsync(); // Exposes internal structure and sensitive data
}
```

### Dependency Injection Patterns

#### Service Registration
```csharp
// In RepositoriesAndServicesConfigurationExtensions.cs
public static IServiceCollection AddScopedServices(this IServiceCollection services)
{
    services.AddScoped<IServicesService, ServicesService>();
    services.AddScoped<IOrganizationService, OrganizationService>();
    return services;
}

public static IServiceCollection AddScopedRepositories(this IServiceCollection services)
{
    services.AddScoped<IServiceRepository, ServiceRepository>();
    services.AddScoped<IOrganizationRepository, OrganizationRepository>();
    return services;
}
```

### AutoMapper Patterns

#### Mapping Profile
```csharp
public class DtoMapperProfile : Profile
{
    public DtoMapperProfile()
    {
        CreateMap<Service, ServiceDTO>();
        CreateMap<CreateServiceRequest, Service>();
        CreateMap<EditServiceRequest, Service>();
        
        // Complex mapping with custom logic
        CreateMap<Policy, PolicyDTO>()
            .ForMember(dest => dest.CanView, opt => opt.Ignore())
            .ForMember(dest => dest.CanCreate, opt => opt.Ignore())
            .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
            .ForMember(dest => dest.CanDelete, opt => opt.Ignore());
    }
}
```

## Build and Development Requirements

### Build Process
The `npm run build` script is the complete build process that includes:
- **Linting**: Code quality checks
- **Unit Tests**: All tests must pass
- **Compilation**: TypeScript compilation and bundling

**Important**: This is what the build pipeline executes. You must run it locally before submitting a PR to ensure all steps pass with no errors or warnings.

### Testing Requirements
- **Unit Test Coverage**: Cover implementation with unit tests where it makes sense
- **PR Completion**: PRs won't be completed until unit testing is at least discussed
- **Test Scripts**:
  - `npm run test`: Full Chrome instance for debugging with coverage reports
  - `npm run test-ci`: Headless Chrome (build pipeline) - verify before PR submission

## Frontend Angular Conventions

### Project Structure
```
src/app/
├── core/                     # Core application components
│   ├── components/           # Layout components (header, sidebar, footer)
│   └── services/             # Core services (auth, identity)
├── shared/                   # Shared utilities
│   ├── components/           # Reusable UI components
│   ├── services/             # Shared services (API, notification)
│   ├── models/               # TypeScript interfaces
│   ├── guards/               # Route guards
│   ├── interceptors/         # HTTP interceptors
│   └── utils/                # Utility functions
├── modules/                  # Feature modules
│   ├── home/                 # Dashboard module
│   ├── device-management/    # Device management features
│   ├── organizations/        # Organization management
│   └── {feature}/            # Other feature modules
├── app.component.ts          # Root component
├── app.config.ts             # Application configuration
└── app.routes.ts             # Route configuration
```

### Component Patterns

#### Standalone Component Structure
```typescript
@Component({
    selector: 'app-home',
    imports: [NotificationSummaryComponent, CommonModule],
    templateUrl: './home.component.html',
    styleUrl: './home.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class HomeComponent {
    private readonly homeService = inject(HomeService);
    private readonly notificationService = inject(NotificationService);

    // Use signals for reactive state
    protected readonly homeScreenData = toSignal(
        this.homeService.getHomeScreenData().pipe(map(res => res.data))
    );

    // Use computed for derived state
    protected readonly hasNotifications = computed(() => 
        this.homeScreenData()?.notifications?.length > 0
    );

    // Use input/output functions
    organizationId = input.required<number>();
    dataLoaded = output<boolean>();

    ngOnInit(): void {
        // Component initialization logic
    }
}
```

#### Template Patterns
```html
<!-- Use control flow syntax -->
@if (homeScreenData(); as data) {
    <div class="dashboard">
        <h1>{{ data.heading }}</h1>
        @if (data.subHeading) {
            <p>{{ data.subHeading }}</p>
        }
    </div>
}

<!-- Use @for for loops -->
@for (notification of notifications(); track notification.id) {
    <app-notification-item [notification]="notification" />
}

<!-- Use class bindings instead of ngClass -->
<div [class.active]="isActive()" [class.disabled]="!isEnabled()">
    Content
</div>

<!-- Use style bindings instead of ngStyle -->
<div [style.width.px]="width()" [style.color]="textColor()">
    Content
</div>
```

#### Access Modifiers
```typescript
@Component({...})
export class ExampleComponent {
    // Private: Used inside component code only
    private readonly service = inject(ExampleService);
    private internalState = signal(false);

    // Protected: Used in templates
    protected readonly data = toSignal(this.service.getData());
    protected readonly isLoading = computed(() => this.data() == null);

    // Public: Inputs, outputs, and lifecycle hooks
    public organizationId = input.required<number>();
    public dataLoaded = output<boolean>();

    // Don't expose public methods for testing - use fixture instead
}
```

#### Template Performance Guidelines
**Critical**: Avoid function calls in template expressions to prevent performance issues.

```typescript
// ❌ Bad: Function calls in templates (called on every change detection)
@Component({
    template: '<i [class]="getIconClass()"></i>'
})
export class BadComponent {
    getIconClass(): string {
        return this.icon + (this.show ? ' enabled' : ' disabled');
    }
}

// ✅ Good: Computed property
@Component({
    template: '<i [class]="iconClass"></i>'
})
export class GoodComponent implements OnInit {
    iconClass = '';

    ngOnInit(): void {
        this.iconClass = this.icon + (this.show ? ' enabled' : ' disabled');
    }
}

// ✅ Better: Using signals
@Component({
    template: '<i [class]="iconClass()"></i>'
})
export class BetterComponent {
    iconClass = computed(() =>
        this.icon() + (this.show() ? ' enabled' : ' disabled')
    );
}
```

### Service Patterns

#### Service Structure
```typescript
@Injectable({
    providedIn: 'root'
})
export class HomeService {
    private readonly apiService = inject(ApiService);
    private readonly endpoint = 'home';

    getHomeScreenData(): Observable<ApiDataResult<HomeScreenData>> {
        return this.apiService.get<ApiDataResult<HomeScreenData>>(this.endpoint);
    }

    getNotifications(organizationId: number, request: IPagination): Observable<ApiDataResult<HomeNotification[]>> {
        return this.apiService.get<ApiDataResult<HomeNotification[]>>(
            `${this.endpoint}/notifications/${organizationId}`, 
            request
        );
    }

    createNotification(request: CreateNotificationRequest): Observable<ApiResult> {
        return this.apiService.post<ApiResult, CreateNotificationRequest>(
            `${this.endpoint}/notifications`, 
            request
        );
    }
}
```

### TypeScript Interface Patterns

#### Model Interfaces
```typescript
// Entity models
export interface Organization {
    organizationId: number;
    name: string;
    companyShortName: string;
    isActive: boolean;
    parentOrganizationId?: number;
    createdDate: Date;
    modifiedDate?: Date;
}

// Request models
export interface CreateOrganizationRequest {
    name: string;
    companyShortName: string;
    parentOrganizationId?: number;
}

// Response models
export interface ApiDataResult<T> {
    data: T;
    message?: string;
    success: boolean;
}

// Enum definitions
export enum NotificationType {
    Info = 'info',
    Success = 'success',
    Warning = 'warning',
    Error = 'error'
}
```

### Routing Patterns

#### Route Configuration
```typescript
export const routes: Routes = [
    {
        path: '',
        loadComponent: () => import('@app/core/components/authenticated-layout/authenticated-layout.component')
            .then(m => m.AuthenticatedLayoutComponent),
        canActivate: [AuthGuard],
        children: [
            {
                path: 'home',
                loadChildren: () => import('./modules/home/<USER>').then(m => m.routes),
                canActivate: [RegisteredGuard, ApprovedGuard]
            },
            {
                path: 'device-management',
                loadChildren: () => import('./modules/device-management/device-management.routes').then(m => m.routes),
                canActivate: [AllowIfMenuItemIsPresentGuard]
            }
        ]
    }
];
```

#### Feature Route Configuration
```typescript
// home.routes.ts
export const routes: Routes = [
    {
        path: '',
        loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent)
    },
    {
        path: 'notifications',
        loadComponent: () => import('./components/notifications/notifications.component').then(m => m.NotificationsComponent)
    }
];
```

### State Management Patterns

#### Signal-based State
```typescript
@Injectable({
    providedIn: 'root'
})
export class UserContextService {
    private readonly _currentUser = signal<UserContext | null>(null);

    // Read-only signal
    readonly currentUser = this._currentUser.asReadonly();

    // Computed values
    readonly isAuthenticated = computed(() => this.currentUser() !== null);
    readonly isAdmin = computed(() => this.currentUser()?.roles?.includes('Admin') ?? false);

    setCurrentUser(user: UserContext): void {
        this._currentUser.set(user);
    }

    updateUserProperty<K extends keyof UserContext>(key: K, value: UserContext[K]): void {
        this._currentUser.update(current => current ? { ...current, [key]: value } : null);
    }
}
```

## Data Layer Conventions

### Entity Framework Patterns

#### DbContext Configuration
```csharp
public class MyAdaptiveCloudContext : DbContext
{
    public MyAdaptiveCloudContext(DbContextOptions<MyAdaptiveCloudContext> options) : base(options) { }

    // Entity sets
    public DbSet<Organization> Organization { get; set; }
    public DbSet<User> User { get; set; }
    public DbSet<UserRole> UserRole { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Global query filters
        modelBuilder.Entity<Organization>().HasQueryFilter(p => p.IsActive);
        modelBuilder.Entity<User>().HasQueryFilter(p => p.Organization.IsActive);

        // Discriminator configuration
        modelBuilder.Entity<Member>().HasQueryFilter(p => p.Organization.IsActive)
            .HasDiscriminator<bool>("is_contact")
            .HasValue<Contact>(true)
            .HasValue<User>(false);

        // Configure relationships
        ConfiguratorHelper.ConfigureOrganizationMappings(modelBuilder);

        base.OnModelCreating(modelBuilder);
    }
}
```

#### Entity Configuration
```csharp
public static void ConfigureOrganizationMappings(ModelBuilder modelBuilder)
{
    modelBuilder.Entity<OrganizationMapping>()
        .HasQueryFilter(p => p.Organization.IsActive)
        .HasDiscriminator<string>("Application")
        .HasValue<BillingDBOrganizationMapping>(Applications.BillingDB.ToString())
        .HasValue<CloudInfraOrganizationMapping>(Applications.CloudInfra.ToString())
        .HasValue<ConnectWiseOrganizationMapping>(Applications.ConnectWise.ToString());
}
```

### Repository Patterns

#### Repository Interface
```csharp
public interface IOrganizationRepository
{
    Task<Organization> GetByIdAsync(int id, bool asNoTracking = false);
    Task<List<Organization>> GetAllAsync(bool asNoTracking = false);
    Task<List<Organization>> GetByParentIdAsync(int parentId, bool asNoTracking = false);
    Task<Organization> CreateAsync(Organization entity);
    Task UpdateAsync(Organization entity);
    Task DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
}
```

#### Repository Implementation
```csharp
public class OrganizationRepository : IOrganizationRepository
{
    private readonly MyAdaptiveCloudContext _context;
    private readonly ReadOnlyMyAdaptiveCloudContext _readOnlyContext;

    public OrganizationRepository(
        MyAdaptiveCloudContext context,
        ReadOnlyMyAdaptiveCloudContext readOnlyContext)
    {
        _context = context;
        _readOnlyContext = readOnlyContext;
    }

    public async Task<Organization> GetByIdAsync(int id, bool asNoTracking = false)
    {
        var query = _readOnlyContext.Organization.Where(o => o.OrganizationId == id);
        return await (asNoTracking ? query.AsNoTracking() : query).FirstOrDefaultAsync();
    }

    public async Task<Organization> CreateAsync(Organization entity)
    {
        _context.Organization.Add(entity);
        await _context.SaveChangesAsync();
        return entity;
    }
}
```

### Entity Patterns

#### Entity Model Structure
```csharp
public class Organization
{
    public int OrganizationId { get; set; }
    public string Name { get; set; }
    public string CompanyShortName { get; set; }
    public bool IsActive { get; set; }
    public int? ParentOrganizationId { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }

    // Navigation properties
    public virtual Organization ParentOrganization { get; set; }
    public virtual ICollection<Organization> ChildOrganizations { get; set; }
    public virtual ICollection<User> Users { get; set; }
    public virtual ICollection<UserRole> UserRoles { get; set; }
}
```

#### DTO Patterns
```csharp
public class OrganizationDTO
{
    public int OrganizationId { get; set; }
    public string Name { get; set; }
    public string CompanyShortName { get; set; }
    public bool IsActive { get; set; }
    public int? ParentOrganizationId { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }

    // Computed properties
    public string FullPath { get; set; }
    public int ChildCount { get; set; }
    public bool CanEdit { get; set; }
    public bool CanDelete { get; set; }
}
```

## Database Schema Conventions

### Table Naming Conventions

#### Naming Convention Guidelines
For **new databases**, use underscore (_) and lowercase letters:

```sql
-- ✅ Good: New database naming
organization_mapping
user_permissions
device_alert_threshold

-- ⚠️ Legacy: MyAdaptiveCloud database uses CamelCase (keep for consistency)
Organization
UserRole
DeviceFolder
```

**Important**: The MyAdaptiveCloud database uses CamelCase for historical reasons. Keep using CamelCase in this database for consistency, but use snake_case for all other databases.

#### Primary Tables
```sql
-- PascalCase for main business entities (MyAdaptiveCloud DB)
Organization
User
UserRole
Permission
DeviceFolder
AlertRule

-- snake_case for operational tables (other databases)
device_alert
message_attachment
organization_device_alert_threshold
```

#### Junction Tables
```sql
-- Combine entity names
UserRole
OrganizationMapping
NotificationRecipient

-- Or use descriptive names
AcCwAccountMap
AcCwVmMap
```

### Column Naming Conventions

#### Primary Keys
```sql
-- Pattern: {TableName}Id
OrganizationId INT PRIMARY KEY AUTO_INCREMENT
UserId INT PRIMARY KEY AUTO_INCREMENT
DeviceFolderId INT PRIMARY KEY AUTO_INCREMENT
```

#### Foreign Keys
```sql
-- Match referenced table's primary key name
organization_id INT NOT NULL
user_id INT NOT NULL
parent_organization_id INT NULL

-- Reference Organization.OrganizationId
FOREIGN KEY (organization_id) REFERENCES Organization (OrganizationId)
```

#### Standard Columns
```sql
-- Audit columns
created_by INT NOT NULL
created_on DATETIME NOT NULL
updated_by INT NULL
updated_on DATETIME NULL

-- Status columns
is_active BOOLEAN DEFAULT TRUE
is_enabled BOOLEAN DEFAULT TRUE
```

### Liquibase Migration Patterns

#### Migration File Structure
```sql
-- liquibase formatted sql

-- changeset author:uuid context:environment
CREATE TABLE IF NOT EXISTS invitation_code (
    invitation_code_id INT(11) AUTO_INCREMENT PRIMARY KEY,
    organization_id INT(11) NOT NULL,
    expiration_date DATETIME NOT NULL,
    code VARCHAR(255),
    use_count INT DEFAULT 0,
    created_by INT(11) NOT NULL,
    created_on DATETIME NOT NULL,
    updated_by INT(11) NULL DEFAULT NULL,
    updated_on DATETIME NULL DEFAULT NULL,
    UNIQUE INDEX ux_code (code),
    CONSTRAINT fk_invitation_code_organization_id_organization_OrganizationId
        FOREIGN KEY (organization_id) REFERENCES Organization (OrganizationId),
    CONSTRAINT fk_invitation_code_created_by_user_user_id
        FOREIGN KEY (created_by) REFERENCES User (UserId)
);
```

#### Safe DDL Patterns
```sql
-- Always use conditional DDL
ALTER TABLE IF EXISTS device_alert
ADD COLUMN IF NOT EXISTS organization_device_alert_threshold_id INT NULL DEFAULT NULL;

-- Safe constraint addition
ALTER TABLE IF EXISTS device_alert
ADD CONSTRAINT fk_device_alert_threshold
    FOREIGN KEY IF NOT EXISTS (organization_device_alert_threshold_id)
    REFERENCES organization_device_alert_threshold (organization_device_alert_threshold_id);

-- Safe index creation
CREATE INDEX IF NOT EXISTS ix_organization_mapping_application
ON OrganizationMapping (Application);

-- Safe table operations
DROP TABLE IF EXISTS table_name;
DROP PROCEDURE IF EXISTS proc_name;
```

#### Safe Test Data Insertion
```sql
-- Pattern for safe test data with foreign key dependencies
INSERT IGNORE INTO `user` (`FirstName`, `Email`, `LastName`, `Password`, `CreatedDate`)
    VALUES ('Local Admin', '<EMAIL>', 'Local Admin', '$2a$11$...', now());

SET @RowCount = ROW_COUNT();
SELECT @UserId := CASE
                  WHEN @RowCount = 0 THEN (SELECT UserId FROM User WHERE Email= '<EMAIL>')
                  ELSE LAST_INSERT_ID()
                  END;

-- Use resolved @UserId for dependent inserts
INSERT INTO `user_organization` (`OrganizationId`, `UserId`)
    VALUES (@RootOrganizationId, @UserId);
```

#### Feature Flag Implementation
```sql
-- Two-statement pattern for feature flags:

-- Statement 1: Set to false for all environments (context: main)
-- changeset author:uuid context:main
CALL SetConfigurationValueV2('Feature Flags', 'FeatureName', 'false', 'input', 0);

-- Statement 2: Override to true for dev/test (context: dev,test)
-- changeset author:uuid context:"dev,test"
CALL SetConfigurationValueV2('Feature Flags', 'FeatureName', 'true', 'input', 0);
```

### Foreign Key Naming Conventions

#### Naming Patterns
```sql
-- Pattern 1: FK_{ChildTable}_{ParentTable}_{ColumnName}
CONSTRAINT FK_UserRole_User_UserId
    FOREIGN KEY (UserId) REFERENCES User (UserId)

-- Pattern 2: fk_{child_table}_{parent_table}_{column_name}
CONSTRAINT fk_device_alert_organization_organization_id
    FOREIGN KEY (organization_id) REFERENCES Organization (OrganizationId)

-- Pattern 3: Descriptive names for complex relationships
CONSTRAINT fk_org_dev_al_th_id_org_dev_al_th_org_dev_al_th_id
    FOREIGN KEY (organization_device_alert_threshold_id)
    REFERENCES organization_device_alert_threshold (organization_device_alert_threshold_id)
```

### Index Naming Conventions

#### Index Types
```sql
-- Unique indexes: ux_{table}_{column(s)}
UNIQUE INDEX ux_organization_id_application (OrganizationId, Application)
UNIQUE INDEX ux_code (code)

-- Regular indexes: ix_{table}_{column(s)}
INDEX ix_organization_mapping_application (Application)
INDEX ix_device_alert_organization_id (organization_id)

-- Composite indexes: include all relevant columns
INDEX ix_user_organization_active (OrganizationId, IsActive)
```

## API Design Patterns

### Response Model Patterns

#### Standard Response Types
```csharp
// Base response for operations without data
public class ApiResult
{
    public string Message { get; set; }
    public bool Success { get; set; } = true;
}

// Response with single data item
public class ApiDataResult<T> : ApiResult
{
    public T Data { get; set; }
}

// Response with collection and count
public class ApiDataSetResult<T> : ApiDataResult<T>
{
    public int TotalCount { get; set; }
}
```

#### Usage Examples
```csharp
// Single item response
[HttpGet("{id:int}")]
public async Task<ActionResult<ApiDataResult<ServiceDTO>>> GetService([FromRoute] int id)
{
    var service = await _serviceService.GetById(id);
    return new ApiDataResult<ServiceDTO>
    {
        Data = service
    };
}

// Collection response
[HttpGet("{organizationId:int}/list")]
public async Task<ActionResult<ApiDataSetResult<List<ServiceDTO>>>> GetServices([FromRoute] int organizationId)
{
    var services = await _serviceService.GetList(organizationId);
    return new ApiDataSetResult<List<ServiceDTO>>
    {
        Data = services,
        TotalCount = services.Count
    };
}

// Operation response
[HttpPost]
public async Task<ActionResult<ApiResult>> CreateService([FromBody] CreateServiceRequest request)
{
    await _serviceService.CreateService(request);
    return new ApiResult
    {
        Message = "Service created successfully"
    };
}
```

### Request Model Patterns

#### Request Naming
```csharp
// CRUD operations
public class CreateServiceRequest { }
public class EditServiceRequest { }
public class DeleteServiceRequest { }

// Specific operations
public class AssignUserRoleRequest { }
public class UpdatePasswordRequest { }
public class SearchOrganizationsRequest { }

// List/Filter requests
public class ServiceListRequest : BaseListRequest { }
public class OrganizationFilterRequest { }
```

#### Request Structure
```csharp
public class CreateServiceRequest
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; }

    [StringLength(500)]
    public string Description { get; set; }

    [Required]
    public int OrganizationId { get; set; }

    public bool IsEnabled { get; set; } = true;
}

public class BaseListRequest
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string SortBy { get; set; }
    public string SortDirection { get; set; } = "asc";
    public string SearchTerm { get; set; }
}
```

### Route Patterns

#### RESTful Routes
```csharp
[Route("api/[controller]")]
public class ServiceController : AuthenticatedControllerBase
{
    // GET api/service/{organizationId}/list
    [HttpGet("{organizationId:int}/list")]
    public async Task<ActionResult<ApiDataSetResult<List<ServiceDTO>>>> GetServices([FromRoute] int organizationId) { }

    // GET api/service/{id}
    [HttpGet("{id:int}")]
    public async Task<ActionResult<ApiDataResult<ServiceDTO>>> GetService([FromRoute] int id) { }

    // POST api/service
    [HttpPost]
    public async Task<ActionResult<ApiResult>> CreateService([FromBody] CreateServiceRequest request) { }

    // PUT api/service/{id}
    [HttpPut("{id:int}")]
    public async Task<ActionResult<ApiResult>> UpdateService([FromRoute] int id, [FromBody] EditServiceRequest request) { }

    // DELETE api/service/{id}
    [HttpDelete("{id:int}")]
    public async Task<ActionResult<ApiResult>> DeleteService([FromRoute] int id) { }
}
```

#### Custom Route Patterns
```csharp
// Nested resource routes
[HttpGet("{organizationId:int}/services/{serviceId:int}/configurations")]
public async Task<ActionResult<ApiDataResult<List<ServiceConfigurationDTO>>>> GetServiceConfigurations(
    [FromRoute] int organizationId,
    [FromRoute] int serviceId) { }

// Action-based routes
[HttpPost("{serviceId:int}/assign-organization")]
public async Task<ActionResult<ApiResult>> AssignOrganization(
    [FromRoute] int serviceId,
    [FromBody] AssignOrganizationRequest request) { }

// Query parameter routes
[HttpGet("search")]
public async Task<ActionResult<ApiDataSetResult<List<ServiceDTO>>>> SearchServices(
    [FromQuery] ServiceSearchRequest request) { }
```

### Validation Patterns

#### Data Annotations
```csharp
public class CreateServiceRequest
{
    [Required(ErrorMessage = "Service name is required")]
    [StringLength(100, ErrorMessage = "Service name cannot exceed 100 characters")]
    public string Name { get; set; }

    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string ContactEmail { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Organization ID must be greater than 0")]
    public int OrganizationId { get; set; }

    [Url(ErrorMessage = "Invalid URL format")]
    public string ServiceUrl { get; set; }
}
```

#### Custom Validation Attributes
```csharp
public class EmailsAddressAttribute : ValidationAttribute
{
    public override bool IsValid(object value)
    {
        if (value is string emails)
        {
            var emailList = emails.Split(',', StringSplitOptions.RemoveEmptyEntries);
            return emailList.All(email => new EmailAddressAttribute().IsValid(email.Trim()));
        }
        return true;
    }
}

// Usage
[EmailsAddress(ErrorMessage = "One or more email addresses are invalid")]
public string NotificationEmails { get; set; }
```

## Error Handling and Logging

### Exception Handling Patterns

#### Custom Exceptions
```csharp
public class NotFoundException : Exception
{
    public string EntityId { get; }

    public NotFoundException(string entityId, string message) : base(message)
    {
        EntityId = entityId;
    }

    public NotFoundException(int entityId, string message) : this(entityId.ToString(), message) { }
}

public class BadRequestException : Exception
{
    public BadRequestException(string message) : base(message) { }
}

// Domain-specific exceptions
public class OntapApiException : BadRequestException
{
    public OntapApiException(string message) : base(message) { }
}
```

#### Global Exception Filter
```csharp
public class HttpResponseExceptionFilter : IAsyncExceptionFilter
{
    private readonly ILogger<HttpResponseExceptionFilter> _logger;

    public HttpResponseExceptionFilter(ILogger<HttpResponseExceptionFilter> logger)
    {
        _logger = logger;
    }

    public Task OnExceptionAsync(ExceptionContext context)
    {
        if (context.Exception is NotFoundException notFoundException)
        {
            _logger.LogInformation(notFoundException.Message, notFoundException.EntityId);
            context.Result = new NotFoundObjectResult(new ApiResult
            {
                Message = notFoundException.Message
            });
            context.ExceptionHandled = true;
        }
        else if (context.Exception is BadRequestException badRequestException)
        {
            _logger.LogInformation(badRequestException.Message);
            context.Result = new BadRequestObjectResult(new ApiResult
            {
                Message = badRequestException.Message
            });
            context.ExceptionHandled = true;
        }

        return Task.CompletedTask;
    }
}
```

### Logging Patterns

#### Custom Database Logger
```csharp
public class DbLogger : ILogger
{
    private const int MAX_LENGTH = 65535;
    private readonly DbLoggerOptions _config;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DbLogger(DbLoggerOptions config, IHttpContextAccessor httpContextAccessor)
    {
        _config = config;
        _httpContextAccessor = httpContextAccessor;
    }

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
    {
        if (!IsEnabled(logLevel)) return;

        var message = formatter(state, exception);
        var logEntry = new LogEntry
        {
            Level = logLevel.ToString(),
            Message = message.Length > MAX_LENGTH ? message.Substring(0, MAX_LENGTH) : message,
            Exception = exception?.ToString(),
            Timestamp = DateTime.UtcNow,
            UserId = GetCurrentUserId(),
            RequestId = GetRequestId()
        };

        // Save to database
        SaveLogEntry(logEntry);
    }
}
```

#### Request Logging Middleware
```csharp
public class HttpRequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<HttpRequestLoggingMiddleware> _logger;

    public HttpRequestLoggingMiddleware(RequestDelegate next, ILogger<HttpRequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var requestId = Guid.NewGuid().ToString();
        context.Items["RequestId"] = requestId;

        _logger.LogInformation("Request started: {Method} {Path} [{RequestId}]",
            context.Request.Method, context.Request.Path, requestId);

        var stopwatch = Stopwatch.StartNew();

        try
        {
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogInformation("Request completed: {Method} {Path} [{RequestId}] - {StatusCode} in {ElapsedMs}ms",
                context.Request.Method, context.Request.Path, requestId,
                context.Response.StatusCode, stopwatch.ElapsedMilliseconds);
        }
    }
}
```

### Frontend Error Handling

#### HTTP Error Interceptor
```typescript
@Injectable()
export class ApiRequestInterceptor implements HttpInterceptor {
    private readonly notificationService = inject(NotificationService);
    private readonly default403Message = 'You do not have permission to complete the required action';

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        return next.handle(req).pipe(
            catchError((error: HttpErrorResponse) => this.handleError(error))
        );
    }

    private handleError(error: HttpErrorResponse): Observable<never> {
        switch (error.status) {
            case 400:
                this.handleValidationErrors(error);
                return throwError(() => error);
            case 401:
                this.handleUnauthorized();
                break;
            case 403:
                this.notificationService.notify(this.default403Message, NotificationType.Error);
                break;
            case 404:
                return throwError(() => error);
            default:
                this.notificationService.notify('An unexpected error occurred', NotificationType.Error);
                break;
        }
        return EMPTY;
    }

    private handleValidationErrors(error: HttpErrorResponse): void {
        const validationErrors = error?.error?.errors;
        if (validationErrors) {
            for (const key in validationErrors) {
                if (Array.isArray(validationErrors[key])) {
                    const errorMessages = validationErrors[key] as string[];
                    errorMessages.forEach(message => {
                        this.notificationService.notify(message, NotificationType.Error);
                    });
                }
            }
        }
    }
}
```

## Anti-Patterns to Avoid

### Backend Anti-Patterns

#### ❌ Don't: Direct DbContext in Controllers
```csharp
// BAD
public class ServiceController : ControllerBase
{
    private readonly MyAdaptiveCloudContext _context;

    [HttpGet]
    public async Task<List<Service>> GetServices()
    {
        return await _context.Service.ToListAsync(); // Direct data access
    }
}
```

#### ✅ Do: Use Service Layer
```csharp
// GOOD
public class ServiceController : AuthenticatedControllerBase
{
    private readonly IServicesService _serviceService;

    [HttpGet("{organizationId:int}/list")]
    public async Task<ActionResult<ApiDataSetResult<List<ServiceDTO>>>> GetServices([FromRoute] int organizationId)
    {
        var result = await _serviceService.GetList(organizationId);
        return new ApiDataSetResult<List<ServiceDTO>>
        {
            Data = result,
            TotalCount = result.Count,
        };
    }
}
```

#### ❌ Don't: Return Entity Models from API
```csharp
// BAD
[HttpGet("{id}")]
public async Task<Service> GetService(int id)
{
    return await _serviceService.GetServiceEntity(id); // Exposing internal structure
}
```

#### ✅ Do: Return DTOs
```csharp
// GOOD
[HttpGet("{id:int}")]
public async Task<ActionResult<ApiDataResult<ServiceDTO>>> GetService([FromRoute] int id)
{
    var result = await _serviceService.GetById(id);
    return new ApiDataResult<ServiceDTO>
    {
        Data = result
    };
}
```

#### ❌ Don't: Catch and Swallow Exceptions
```csharp
// BAD
public async Task<ServiceDTO> GetService(int id)
{
    try
    {
        var service = await _repository.GetByIdAsync(id);
        return _mapper.Map<ServiceDTO>(service);
    }
    catch
    {
        return null; // Swallowing exception
    }
}
```

#### ✅ Do: Let Exceptions Bubble Up or Handle Specifically
```csharp
// GOOD
public async Task<ServiceDTO> GetService(int id)
{
    var service = await _repository.GetByIdAsync(id);
    if (service == null)
    {
        throw new NotFoundException(id, "Service not found");
    }
    return _mapper.Map<ServiceDTO>(service);
}
```

### Frontend Anti-Patterns

#### ❌ Don't: Use ngClass and ngStyle
```html
<!-- BAD -->
<div [ngClass]="{'active': isActive, 'disabled': !isEnabled}">
<div [ngStyle]="{'width': width + 'px', 'color': textColor}">
```

#### ✅ Do: Use Class and Style Bindings
```html
<!-- GOOD -->
<div [class.active]="isActive()" [class.disabled]="!isEnabled()">
<div [style.width.px]="width()" [style.color]="textColor()">
```

#### ❌ Don't: Use Constructor Injection in Components
```typescript
// BAD
@Component({...})
export class HomeComponent {
    constructor(
        private homeService: HomeService,
        private notificationService: NotificationService
    ) {}
}
```

#### ✅ Do: Use inject() Function
```typescript
// GOOD
@Component({...})
export class HomeComponent {
    private readonly homeService = inject(HomeService);
    private readonly notificationService = inject(NotificationService);
}
```

#### ❌ Don't: Mutate Signals Directly
```typescript
// BAD
const userSignal = signal<User | null>(null);
userSignal().name = 'New Name'; // Direct mutation
```

#### ✅ Do: Use set() or update()
```typescript
// GOOD
const userSignal = signal<User | null>(null);
userSignal.update(user => user ? { ...user, name: 'New Name' } : null);
```

#### ❌ Don't: Subscribe in Components Without Cleanup
```typescript
// BAD
ngOnInit() {
    this.homeService.getData().subscribe(data => {
        this.data = data; // Memory leak potential
    });
}
```

#### ✅ Do: Use Signals or takeUntilDestroyed
```typescript
// GOOD - Using signals
protected readonly data = toSignal(this.homeService.getData());

// GOOD - Using takeUntilDestroyed
ngOnInit() {
    this.homeService.getData()
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(data => {
            // Handle data
        });
}
```

### Database Anti-Patterns

#### ❌ Don't: Use Generic Column Names
```sql
-- BAD
CREATE TABLE service (
    id INT PRIMARY KEY,
    data TEXT,
    status INT,
    date DATETIME
);
```

#### ✅ Do: Use Descriptive Column Names
```sql
-- GOOD
CREATE TABLE service (
    service_id INT PRIMARY KEY,
    service_name VARCHAR(255),
    description TEXT,
    is_active BOOLEAN,
    created_date DATETIME
);
```

#### ❌ Don't: Skip Foreign Key Constraints
```sql
-- BAD
CREATE TABLE user_role (
    user_id INT,
    role_id INT
    -- No foreign key constraints
);
```

#### ✅ Do: Always Define Foreign Key Constraints
```sql
-- GOOD
CREATE TABLE user_role (
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    CONSTRAINT fk_user_role_user_id FOREIGN KEY (user_id) REFERENCES User (UserId),
    CONSTRAINT fk_user_role_role_id FOREIGN KEY (role_id) REFERENCES Role (RoleId)
);
```

---

This style guide should be used as the authoritative reference for all code written in the MyAdaptiveCloud project. When in doubt, refer to existing code that follows these patterns, and always prioritize consistency with the established conventions.
