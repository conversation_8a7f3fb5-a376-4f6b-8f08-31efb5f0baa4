Modal Service
When showing Confirmation Dialogs, Delete Confirmations or just opening Modal Components we should use the ModalService.


This service internally uses the NgbModal class to show modals, set configurations and return the result.

The modalService has 3 Methods so far:

openConfirmationDialog(data: ConfirmationDialogModel): NgbModalRef

openDeleteConfirmationDialog(header: string, content?: string, confirmButtonText?: string, userAcceptCheckboxText?: string, isHtml?: boolean): NgbModalRef

openModalComponent(component: any, options?: NgbModalOptions): NgbModalRef

With



export class ConfirmationDialogModel {
    public content: string;
    public title?: string;
    public showCancelButton?: boolean;
    public cancelButtonText?: string;
    public confirmButtonText?: string;
    public isDelete?: boolean;
    public userAcceptText?: string;
    public isHTML?: boolean;
}
 

When testing Components that use the modalService we might found that we need a return type for any of the 3 existing methods, in that case a proper solution would be for example:



    let mockModalService: jasmine.SpyObj<ModalService>;
    mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;
    mockModalService.openDeleteConfirmationDialog.and.returnValue({ closed: of(true) } as NgbModalRef);
 

This block of code fakes the call of the modalService and sets a return value of the same type, which will allow the code to normally work as expected.

Submit button
When implementing Modals, and a submit button is needed, be sure to use the shared component app-btn-submit this component prevent the user to trigger multiple submit actions, because it’s linked with the LoadingService so if a request is ongoing the button is automatically disabled.

BtnSubmitComponent



    @Input() disabled = false;
    @Input() btnClasses: string | string[] = ['btn', 'btn-primary'];
    @Output() submitClickEvent = new EventEmitter<boolean>();
Usage example:



 <app-btn-submit [btnClasses]="'ms-2 btn btn-primary'" 
                 [disabled]="formGroup.invalid" 
                 (submitClickEvent)="submit()">
                 {{acceptText}}
 </app-btn-submit>