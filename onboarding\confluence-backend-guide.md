API 

Controllers

Controllers live in the MyAdaptiveCloud.Api.Controllers namespace in the MyAdaptiveCloud project. 

Controllers inherit from AuthenticatedControllerBase, which includes Authorization and Api conventions for Swagger by default.

Refer to https://ippathways.atlassian.net/wiki/spaces/MYADAPTIVE/pages/1002209281/UI+Best+practices+and+Patterns#Do.4  for controller action return types.

In controller actions that returns a list of records, and these records include actions that should be restricted by permissions, use the UserActionResultAttribute type filter.

Keep controllers as lean as possible. Move logic to services and use controllers only for orchestrating service calls.

Make sure that your changes are reflected in the Swagger document as expected. Swagger is accessible on {appUrl}/Swagger, i.e.: https://localhost:5001/swagger.

Do

Return NotFound action when the endpoint refers to a specific entity by ID (either GET or PUT). Return a ApiDataResult or ApiDataSetResult with an empty list of T when the endpoint refers to a list of records, and the service response does not return any records.

Validate request models using DataAttributes and IValidatableObject (see MyAdaptiveCloud.Api.Requests.Profile.ResetPasswordRequest). Map request models to the service layer models using Automapper.

Authorization Filters: All endpoints must have an Authorization Filter. The implementation of the filter should follow these guidelines:

Preferred: Authorize against the organization linked to the entity that is being requested. Pass ONLY the id of this entity and resolve the linked organization id internally in the filter. See EscalationChainAuthorizeFilter. Mostly supported by GET single entity endpoints, PUT (update). and DELETE.

Fallback: In cases where it is not possible to supply a specific entity id, use the organizationId from the user context and the OrgAuthorize or RootOrgAuthorize filters. Use cases: GET lists or POST (create).

Don’t

Pass the currently logged in user id from the UI. Always retrieve from the claims, using the method in IIdentityService.

Use DbContext directly from controllers. Use service methods instead.

Services

Services live in the MyAdaptiveCloud.Services project. This project has access to the Data layer and it is accessible from the API layer.

When you need to return a NotFound or BadRequest result, and this is based on an action in a service, throw a NotFoundException or a BadRequestException. The MyAdaptiveCloud.Api.Filters.HttpResponseExceptionFilter exception filter intercepts these and returns the appropriate HTTP responses to the client.

Do

Use DTOs to transfer data from a service to the API. When writing a LINQ query against the DbContext, use a Select() to map the output to the DTO.

Don’t

Use data entities directly to transfer data to the API, to avoid potential context leakages and exposing sensitive data.



Repositories and DbContext

Repositories live in the MyAdaptiveCloud.Data project and it is accessible from the Services layer.

A repository class should only reference the relevant DbContext and helper dependencies such as ILogger. It should not reference other repositories.

For entities with a relationship to an Organization, use EF global filters to filter out inactive organizations.

  protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
               ...
               modelBuilder.Entity<EscalationChainDetail>().HasQueryFilter(ecd => ecd.EscalationChain.Organization.IsActive);
        }

To retrieve the organization hierarchy, use MyAdaptiveCloudContext.OrgHierarchy and MyAdaptiveCloudContext,OrgHierarchyWithDistance.The latter adds to the result set the "distance" of each organization from the root. While this often returns more data than necessary, it was an original design decision: Because this area is critical, we don't want to maintain more than two ways of doing it. Furthermore, these sprocs are optimized with the use of CTE, so it will be difficult for, writing it another way, the benefits of bringing less data to be greater than the query itself.



Helpers

Permissions

When working with Permissions you have 2 available functions under MyAdaptiveCloud.Tests/Helpers/PermissionsHelper.cs

byte[] AllPermissionsExcept(params Perms[] permissions)

This functions allows you to get a byte array of permissions where all permissions are true but the ones passed as parameters.

byte[] OnlyPermissions(params Perms[] permissions)

This functions allows you to get a byte array of permissions where all permissions are false but the ones passed as parameters.

Dates

Use DateTimeOffset instead of DateTime to represent Dates in Data entities, DTOs and ViewModels. This is to ensure UTC information is carried around properly.

Always store Dates in UTC format in the Database.

Binding Person data

A class named BasePerson is included in the Common project. This should be used for entities that contain three share data points: FirstName, LastName and Email. This class exposes a FullName property which is resolved internally based on FirstName and LastName.

Note: There is a corresponding BasePerson class in the UI.

Ordering and Paging

Ordering

Order: This extension method sorts the elements of a sequence or query based on the criteria defined in the incoming request.

Example:

reportsRequestDTO = reportsRequestDTO.Order(request);

Here, request inherits from BaseListRequest.

The Order method supports both materialized collections (IEnumerable) and non-materialized queries (IQueryable), enabling server-side ordering in the latter case.

In the following example, ordering is applied using nested properties. It accepts a mapping between request fields and the corresponding properties to sort by:

query = query.Order(request,
    (ReportRequestField.CreatedByEmail, r => r.CreatedByPerson.Email),
    (ReportRequestField.Name, r => r.ReportRequest.Name),
    (ReportRequestField.ReportType, r => r.ReportRequest.ReportType));

This approach allows flexible, strongly-typed ordering for complex objects by mapping sort fields to lambda expressions.

Paging

Paginate This extension method paginates the specified query or collection based on the given request.


Example:

var paginatedQuery = query.Paginate(request);

It is essentially a shortcut for applying the Skip and Take methods:

Skip((request.CurrentPage.Value - 1) * request.PageSize.Value).Take(request.PageSize.Value) 