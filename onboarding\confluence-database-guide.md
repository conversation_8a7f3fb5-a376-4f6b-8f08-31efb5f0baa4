Naming Convention
For table/column/index/etc… naming convention use underscore (_) and lower case letters. This pattern helps to make the names readable, consistent, and easily distinguishable.

For historical reasons, columns in the myadaptivecloud database use CamelCase, which is ok to keep using for consistency (see https://github.com/ippathways/client-center/pull/635#discussion_r1229708102). All other databases should follow the conventions listed here.

Example:

Do:

organization_mapping

Don’t:

OrganizationMapping

Organization_Mapping

Index and Key Naming Convention
<index or key type>_<column 1>_<column 2>_<column n>

index or key type

ix_ none unique index

ux_ unique index

pk_ primary key constraint

fk_ foreign key

Indexes
Do write an index on a column that will be searched by 

Foreign Key Constraints
Do name your foreign key constraints for the foreign key and automatically generated foreign key constraint indexes to have proper naming to be able to tell what the index is for.

Foreign Key Naming Convention
fk_<self table name>_<column foreign key constraint is on>_<target table name>_<target column name>

Example for a foreign key on policy.created_by column to user.user_id

CONSTRAINT `fk_policy_created_by_user_user_id` FOREIGN KEY (created_by) REFERENCES `user` (user_id)

Notice that there is a name (fk_policy_created_by_user_user_id) given after the constraint, this name is used for the foreign key and automatically generated index.  If you don’t specify the name they mariadb will generate an automatic name that is not a user friendly readable name.

Do:

CONSTRAINT `fk_policy_created_by_user_user_id`FOREIGN KEY (created_by) REFERENCES `user` (user_id)

Don’t (While this is valid SQL syntax, it is missing the constraint name):

CONSTRAINT FOREIGN KEY (created_by) REFERENCES `user` (user_id)

Table Primary Key Naming Convention
Do name your primary key with <table_name>_id

This helps with not having to alias id that is referenced all over the place in queries.

Example for primary key for policy table:

Do:

policy_id

Don’t:

id

 

Stored Procedures
In general we are staying away from writing stored procedures unless absolutely necessary.

Primary reason for staying away from writing stored procs is to make sure we can easily write unit tests for business logic in code.

Do Write A Stored Proc
Some of the reasons you may need to write a stored proc.

Performance Reasons

some things to be performant we may need to write a stored proc to leverage DB engine to do its “magic”

recursive (organization hierarchy) type requests to avoid many individual calls from c#

Do NOT Write A Stored Proc
Some of scenarios where we would not write a stored proc.

get_policy

In this example this get policy is a simple select operation with perhaps joining on some other tables.

In this case just write this as a repository in C# code.

Naming Convention
Do start your name with something that makes sense on the operation being performed when it makes sense as in <get|update|delete>_some_operation_name

Example:

Do:

get_agent_summary

Don’t:

agent_summary

With this name even if it will be just getting agent summary its not clear by the name what it is doing

 

Test Data Safe Insertions
Sometimes we need to INSERT test data and, when the tables are linked by foreign keys, it can be tricky. 

The safest way is the following: 



INSERT IGNORE INTO `user` (`FirstName`, `Email`, `LastName`, `Password`, `CreatedDate`) 
    VALUES ('Local Admin', '<EMAIL>', 'Local Admin', '$2a$11$MXMwGFZ.spue6PHWTxn6YOxPvrqrbrhQU33/uExsWO6clWNKI6s/y', now());
SET @RowCount = ROW_COUNT();
SELECT @UserId := CASE 
                  WHEN @RowCount = 0 THEN (SELECT UserId FROM User WHERE Email= '<EMAIL>')
                  ELSE LAST_INSERT_ID() 
                  END;
    INSERT INTO `user_organization` (`OrganizationId`, `UserId`) VALUES (@RootOrganizationId, @UserId);
    SELECT @LastUserOrganization := LAST_INSERT_ID();
    INSERT INTO `user_organization_mapping` (`RoleId`, `CreatedBy`, `CreatedDate`, `IsApproved`, `UserOrganizationId`) VALUES (1, 1, NOW(), 1, @LastUserOrganization);
In this piece of code: 

The INSERT IGNORE will prevent from failures if the INSERT statement failed due to foreign key or indexes violations

SET @RowCount = ROW_COUNT(); line sets a variable @RowCount to the number of rows affected by the previous SQL statement.

If ROW_COUNT is equals to 0, then it means that no user was inserted because the email address already exists (this table contains an unique index). Then, if user was not inserted, it will query on the table to get his ID: WHEN @RowCount = 0 THEN (SELECT UserId FROM User WHERE Email= '<EMAIL>')

That way, variable @UserId always contains the correct value for email: '<EMAIL>' 

 

IF EXISTS: Ensuring a Smooth Flow
Dropping a Table Safely



DROP TABLE IF EXISTS table_name;
 

2. Altering a Table: 




ALTER TABLE IF EXISTS table_name
ADD COLUMN column_name datatype;
3. Dropping a procedure safely



DROP PROCEDURE IF EXISTS proc_name;
IF NOT EXISTS: Guaranteeing Data Integrity
Creating an Index: 




CREATE INDEX IF NOT EXISTS index_name
ON table_name (column1, column2, ...);
 

2. Adding a column: 



ALTER TABLE IF EXISTS table_name
  ADD COLUMN IF NOT EXISTS column_name data_type NULL;
 

3. Adding Foreign Key:



ALTER TABLE table_name
    ADD COLUMN IF NOT EXISTS column_name data_type NOT NULL DEFAULT 0,
    ADD CONSTRAINT `FK_my_fk` FOREIGN KEY IF NOT EXISTS (column_name) REFERENCES `target_table` (target_column);
 

Feature Flags
When creating a new feature flag, two sql statements are needed (see example below):

First one to set the feature flag to false using context main. This will run in all environments. 

Second one to set the feature flag to true, using context dev and test. This will override the previous settings locally and in the lab, leaving prod unchanged.

 -- changeset ebeghe:8f819f26-f375-48af-9082-36e1295481a7 context:main
CALL SetConfigurationValueV2('Feature Flags', 'Tenax', 'false', 'input', 0);
-- changeset jbarrera:B613BDC3-E48C-4393-B5C8-D721183FA42B context:"dev,test"
CALL SetConfigurationValueV2('Feature Flags', 'Tenax', 'true', 'input', 0);