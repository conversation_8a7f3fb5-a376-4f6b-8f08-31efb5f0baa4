Grids
Extend the BaseListComponent<TModel, TFilter = object>  class for grids with server-side paging, sorting and filtering

The service call used to retrieve the grid data should take a IPagination argument. The base class defines a pagination property, that should be assigned to the actual IPagination implementation



export class NotificationsListComponent extends BaseListComponent<Notification> implements OnInit {
    constructor(
        private notificationsService: NotificationsService,
        private userContextService: UserContextService
    ) {
        super(menuService);
        this.pagination = new NotificationsListRequest();
    }
 

Extend the BaseListComponent<TModel, TFilter = object> class for grids with client-side paging, sorting and filtering. These grids should be used in cases where we don’t expect much data returned by the API.

In both cases, in the OnInit lifecycle, configure the table columns, and call the base class with two arguments: The service method used to load the data and the array of columns. Also, the base class exposes a table property with a reference to the grid element, which can be used for further customization.



ngOnInit() {
        const columns: TableColumn[] = [
            {
                name: 'Host Name',
                prop: 'hostname',
                headerTemplate: this.headerTemplate,
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
     ];
    super.initialize(this.agentService.getAgentList.bind(this.agentService, this.userContextService.currentUser.organizationId), columns);
 

Heading



<div class="content-heading">
    Heading
</div>
Plain string, just add the text you want your grid to have as heading

Don’t add any extra <div>, you can add a <span> if youy need to conditionally show icons

Subheading


<div class="content-sub-heading">
  Subheading
</div>
Plain string, a <span> can be used to conditionally show or hide icons

IMPORTANT:  Add it always, it separates the heading from the grid, it must be used even when empty to maintain spacings and structure

Action Buttons


<div class="action-buttons">
    <button *ngIf="permissionService.canCreateUserRole()" class="btn btn-primary" type="button"
            (click)="createUserOrganizationRole()">
            Add Member
    </button>
    <button class="btn btn-primary" type="button" (click)="submitForm()">Update</button>
    <app-back [buttonText]="'Back to Organizations'"></app-back>
</div>
The action buttons div is specifically built to contain the buttons corresponding to all the actions you need to perform on your grid.

As many buttons as needed can be added

Don’t add any HTML element different from <buttons>

Back Button


<app-back [buttonText]="'Back to Organizations'"></app-back>
There is a special component created for the situation where you need a Back button.

The back button component can just receive a string as parameter for the button text, and it will work as expected sending the user back to the last visited page



<app-back *ngIf="showDetails" [buttonText]="'Back to Summary'" [hasCustomBehavior]="true" (clickEvent)="onShowSummary()"></app-back>
There is an extra behavior where the back button can perform a custom functionality when needed, for this to happen it receives a parameter that prevents it from sending the user to the last visited page (hasCustomBehavior), and instead offers the chance to listen for the click event so it can trigger a custom function (clickEvent).

Filters
We have 2 types of filters for tables:

Search term filter


 <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" [placeholder]="column.name"
        [dataItemName]="column.prop">
    </app-auto-search-box>
 This component is used generally inside the #headerTemplate.

It displays a Textbox and a Search Button to trigger the filtering. 

onFilterCriteriaChanged is lives in BaseListComponent and should filter row values according to the 

dataItemName we set. If dataItemName is not setted, by default it will search the inputed term over all table columns.

Custom Filter Popup
This filter will display a button and a popup modal with a form, this form values will be used in the API to filter the query. (See List-log-filter as an example.)

To implement this feature we need a class that represents the filter Model with the properties we want to be able to be filtered, for example:



export class LogListFilter {
    constructor() {
        this.fromDate = new Date();
        this.fromDate.setHours(this.fromDate.getHours() - 12);
    }
    fromDate: Date;
    toDate = new Date();
    userId?: number;
    organizationId?: number;
    viewHttpRequests: LogListFilterRequestView = LogListFilterRequestView.ViewErrorsOnly;
    viewEntityChanges = false;
}
Also we need a component that extends: BaseListFilterComponent<TFilter extends object> .

Here will live all the “observable sources“ for our form values (Drop Down Lists options, mostly), and the implementation of the form itself. 

When some filter value change we need to build accordingly a Request, i.e:



export class UserListRequest implements IPagination {
    pageSize = 25;
    currentPage = 1;
    orderBy = 'firstName';
    orderDir = 'asc';
    firstName = '';
    lastName = '';
    email = '';
}
 This payload will be sended to the API to do the proper queries and then map the response back,

Tests 
In order to keep the tests as tied to the actual UI as we can, you should try to test the actual rows that the grid component generates, to do so please remember to import the table component module, like this



await TestBed.configureTestingModule({
            providers: [
                provideMock(NotificationsService),
                provideMock(ModalService),
                provideMock(UserContextService),
            ],
            declarations: [NotificationsListComponent],
            imports: [
                NgxDatatableModule, // <---- HERE
                SharedModule
            ]
        }).compileComponents();
 

When creating tests, keep in mind we want to test the actual generated rows, to do so there are a couple of HTML5 tools like querySelector() and querySelectorAll(), that returns one or many objects matching some kind of criteria, for example



it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
});
In this test we are getting all the row wrappers, which should match the amount of data we are returning with the data service. Also notice we are using ‘datatable-row-wrapper’ which corresponds to a component we are using, we can also use ‘.class’, ‘#id’ and combinations.

Another useful tool is the angular .query function, which can be used from the debugElement, like this



beforeEach(async () => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
            fixture.detectChanges();
});
Here we are getting an instance of a component using the .query function, which can also support classes, using By.css('.class').

Combining these tools you should have what you need to test every component rendered within the grid.

For examples please check ‘/src/app/modules/notifications/components/notifications-list/notifications-list.component.spec.ts’