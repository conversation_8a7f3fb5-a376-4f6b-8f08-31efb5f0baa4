# Frontend Development Guide

## Table of Contents
- [Architecture Overview](#architecture-overview)
- [Project Structure](#project-structure)
- [Component Architecture](#component-architecture)
- [Routing & Navigation](#routing--navigation)
- [State Management](#state-management)
- [Services & HTTP](#services--http)
- [Guards & Interceptors](#guards--interceptors)
- [UI Components & Styling](#ui-components--styling)
- [Build Configuration](#build-configuration)

## Architecture Overview

The frontend is built with Angular 20 using modern patterns and standalone components:

```
┌─────────────────────────────────────────────────────────────┐
│                    App Component                            │
├─────────────────────────────────────────────────────────────┤
│                Authenticated Layout                        │
├─────────────────────────────────────────────────────────────┤
│    Header    │              Main Content                   │
│   Sidebar    │         (Feature Modules)                  │
│   Footer     │                                            │
├─────────────────────────────────────────────────────────────┤
│              Shared Services & Components                  │
└─────────────────────────────────────────────────────────────┘
```

### Key Architectural Patterns
- **Standalone Components**: No NgModules, using standalone components
- **Lazy Loading**: Feature modules loaded on demand
- **Signal-based State**: NgRx Signals for reactive state management
- **Dependency Injection**: Modern inject() function usage
- **OnPush Change Detection**: Performance optimization

## Project Structure

```
src/app/
├── core/                     # Core application components
│   ├── components/           # Layout components
│   └── services/             # Core services
├── shared/                   # Shared utilities
│   ├── components/           # Reusable UI components
│   ├── services/             # Shared services
│   ├── guards/               # Route guards
│   ├── interceptors/         # HTTP interceptors
│   ├── models/               # TypeScript interfaces
│   └── utils/                # Utility functions
├── modules/                  # Feature modules
│   ├── home/                 # Dashboard module
│   ├── device-management/    # Device management
│   ├── cloud-infrastructure/ # Cloud infrastructure
│   └── organizations/        # Organization management
├── app.component.ts          # Root component
├── app.config.ts             # Application configuration
└── app.routes.ts             # Route configuration
```

## Component Architecture

### Standalone Component Pattern
```typescript
@Component({
    selector: 'app-home',
    imports: [NotificationSummaryComponent],
    templateUrl: './home.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class HomeComponent {
    private readonly homeService = inject(HomeService);
    
    protected readonly homeScreenData = toSignal(
        this.homeService.getHomeScreenData().pipe(map(res => res.data))
    );
}
```

### Component Conventions
- **Standalone Components**: Use standalone components, avoid NgModules
- **OnPush Change Detection**: All components use OnPush strategy
- **Signal-based**: Use signals for reactive data
- **Dependency Injection**: Use inject() function instead of constructor injection
- **Template Syntax**: Use new Angular control flow (@if, @for)
- **Static Properties**: Avoid static properties in components
- **Subscriptions**: Use toSignals or takeUntilDestroyed instead of manual subscriptions

### Access Modifiers
```typescript
@Component({...})
export class ExampleComponent {
    // Private: Used inside component code only
    private readonly service = inject(ExampleService);
    private internalState = signal(false);

    // Protected: Used in templates
    protected readonly data = toSignal(this.service.getData());
    protected readonly isLoading = computed(() => this.data() == null);

    // Public: Inputs, outputs, and lifecycle hooks
    public organizationId = input.required<number>();
    public dataLoaded = output<boolean>();

    // Public lifecycle hooks
    public ngOnInit(): void {
        // Initialization logic
    }

    // Don't expose public methods for testing - use fixture instead
}
```

### Component Migration Guidelines
- **OnPush Migration**: Migrate existing Default change detection components when working on them
- **Time Constraints**: Skip migration only under time constraints or high complexity
- **Standalone Migration**: Prefer importing standalone dependencies over modules

### Layout Components
```typescript
// Authenticated Layout Structure
@Component({
    selector: 'app-authenticated-layout',
    template: `
        @if ((userContextService.currentUser$ | async); as userContext) {
            <div class="wrapper">
                <div class="topnavbar-wrapper">
                    <app-header [userContext]="userContext" />
                </div>
                <div class="main-section-wrapper">
                    @if (userContext.isRegistered && userContext.isApproved) {
                        <div class="aside-container">
                            <app-sidebar [userContext]="userContext" />
                        </div>
                    }
                    <section class="section-container">
                        <div class="content-wrapper">
                            <router-outlet />
                        </div>
                    </section>
                </div>
            </div>
        }
    `
})
```

## Routing & Navigation

### Route Configuration
```typescript
export const routes: Routes = [
    {
        path: '',
        loadComponent: () => import('@app/core/components/authenticated-layout/authenticated-layout.component')
            .then(m => m.AuthenticatedLayoutComponent),
        canActivate: [AuthGuard],
        children: [
            {
                path: 'home',
                loadChildren: () => import('./modules/home/<USER>').then(m => m.routes),
                canActivate: [RegisteredGuard, ApprovedGuard]
            },
            {
                path: 'device-management',
                loadChildren: () => import('./modules/device-management/device-management.routes').then(m => m.routes),
                canActivate: [AllowIfMenuItemIsPresentGuard]
            }
        ]
    }
];
```

### Lazy Loading Pattern
- **Feature Modules**: Each major feature is a separate lazy-loaded module
- **Route-based**: Modules loaded when routes are accessed
- **Code Splitting**: Automatic code splitting for better performance

### Route Guards
```typescript
@Injectable({ providedIn: 'root' })
export class AuthGuard {
    private readonly userService = inject(UserContextService);
    private readonly authService = inject(AuthService);

    canActivate(): boolean {
        const currentUser = this.userService.currentUser;
        if (!currentUser) {
            this.authService.redirectToSSOLogin(true);
            return false;
        }
        return true;
    }
}
```

## State Management

### NgRx Signals Store
```typescript
export const ZoneDomainAccountStore = signalStore(
    withState(initialState),
    withMethods((
        store,
        cloudInfraSessionService = inject(CloudInfrastructureSessionService),
        userContextService = inject(UserContextService),
        domainService = inject(CloudInfraDomainService)
    ) => ({
        loadSubDomainsByDomainId: rxMethod<string>(pipe(
            switchMap(domainId => domainService.getDomainChildrenList(domainId)
                .pipe(tapResponse({
                    next: res => {
                        const updatedDomains = updateSubDomainsByDomainId(domainId, store.domains(), res);
                        patchState(store, state => ({
                            ...state,
                            loadingId: null,
                            domains: [...updatedDomains]
                        }));
                    },
                    error: () => EMPTY
                }))
            )
        ))
    })),
    withHooks({
        onInit: store => {
            store.loadFolders();
        }
    })
);
```

### State Management Patterns
- **Signal Stores**: Feature-specific state management
- **Reactive Methods**: rxMethod for async operations
- **Immutable Updates**: Use patchState for state updates
- **Error Handling**: Consistent error handling patterns

### Local State Management
For components requiring local state management, use `@ngrx/signals`:

```typescript
// Good use case: Components in same tree need to share state
// State not relevant to global application
// State doesn't need to persist after components are destroyed

@Injectable()
export class LocalFeatureStore extends ComponentStore<LocalState> {
    constructor() {
        super(initialState);
    }

    readonly updateLocalData = this.updater((state, data: LocalData) => ({
        ...state,
        data,
        loading: false
    }));

    readonly loadData = this.effect((trigger$: Observable<void>) =>
        trigger$.pipe(
            tap(() => this.patchState({ loading: true })),
            switchMap(() => this.dataService.getData().pipe(
                tapResponse({
                    next: data => this.updateLocalData(data),
                    error: error => this.patchState({ loading: false, error })
                })
            ))
        )
    );
}
```

## Services & HTTP

### Service Pattern
```typescript
@Injectable({ providedIn: 'root' })
export class ApiService {
    private readonly httpClient = inject(HttpClient);
    private readonly loadingService = inject(LoadingService);
    private readonly notificationService = inject(NotificationService);

    public get<TResult>(endpoint: string, params: any = null, showLoader = true): Observable<TResult> {
        let requestId: string;
        if (showLoader) {
            requestId = this.startRequest();
        }
        
        return this.httpClient.get<TResult>(environment.server + endpoint, {
            params: this.queryStringBuilder(params),
            headers: { 'Content-Type': 'application/json' }
        }).pipe(
            finalize(() => {
                if (showLoader) {
                    this.loadingService.endRequest(requestId);
                }
            }),
            catchError(error => this.handleError(error))
        );
    }
}
```

### HTTP Communication
- **Base API Service**: Centralized HTTP operations
- **Loading Management**: Automatic loading indicators
- **Error Handling**: Consistent error handling
- **Type Safety**: Strong typing for API responses

### API Service Guidelines
Each module should have a service class extending `ApiService`:

```typescript
@Injectable({ providedIn: 'root' })
export class UserService extends ApiService {
    private readonly endpoint = 'users';

    getUsers(organizationId: number): Observable<ApiDataResult<User[]>> {
        return this.get<ApiDataResult<User[]>>(`${this.endpoint}/${organizationId}/list`);
    }

    createUser(request: CreateUserRequest): Observable<ApiResult> {
        return this.post<ApiResult, CreateUserRequest>(`${this.endpoint}`, request);
    }
}
```

### RESTful API Patterns
- **GET**: Retrieve data using `ApiDataResult<T>` or `ApiDataSetResult<T>`
- **POST**: Create resources, return `ApiResult` with 201 status
- **PUT**: Update resources with entity ID, return `ApiResult`
- **DELETE**: Remove resources with entity ID, return `ApiResult`

### Request/Response Models
```typescript
// Request models should match C# endpoint models
export class CreateUserRequest {
    public email: string;
    public roleId: number;
    public organizationId: number;
    public firstName: string;
    public lastName: string;
}

// Usage in components
submitForm() {
    this.isSubmitted = true;
    if (this.form.valid) {
        this.userService.createUser(this.form.getRawValue() as CreateUserRequest)
            .pipe(takeUntil(this.destroyed$))
            .subscribe(res => {
                this.notificationService.notify(res.message);
                this.activeModal.close(res);
            });
    }
}
```

### Time Zone Handling
- **Automatic Headers**: ApiService includes client time zone information with each request
- **Server Attribute**: Mark endpoints with `UIClientTimeZoneRequired` attribute
- **Time Zone ID**: Valid TimeZoneId sent in headers automatically

## Guards & Interceptors

### HTTP Interceptor
```typescript
@Injectable()
export class ApiRequestInterceptor implements HttpInterceptor {
    private readonly authService = inject(AuthService);
    private readonly loadingService = inject(LoadingService);
    private readonly notificationService = inject(NotificationService);

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        // Add timezone header
        request = request.clone({
            headers: request.headers.set('X-IPP-UI-Client-TimeZone', 
                Intl.DateTimeFormat().resolvedOptions().timeZone)
        });

        return next.handle(request).pipe(
            catchError(err => this.handleError(err))
        );
    }

    private handleError(error: HttpErrorResponse): Observable<never> {
        this.loadingService.endAllRequests();
        
        switch (error.status) {
            case 401:
                this.authService.redirectToSSOLoginWithPrompt(true);
                break;
            case 403:
                this.notificationService.notify('Access denied', NotificationType.Error);
                break;
        }
        
        return throwError(() => error);
    }
}
```

### Guard Types
- **AuthGuard**: Ensures user is authenticated
- **RegisteredGuard**: Checks if user is registered
- **ApprovedGuard**: Verifies user approval status
- **AllowIfMenuItemIsPresentGuard**: Permission-based access

## UI Components & Styling

### Component Library
The application uses a combination of:
- **Bootstrap 5.3**: Base UI framework
- **Custom Components**: Application-specific components
- **Third-party Libraries**: Specialized components

### Key UI Libraries
```typescript
// Package.json dependencies for UI
"@ng-bootstrap/ng-bootstrap": "^19.0.1",     // Bootstrap components
"@ng-select/ng-select": "^20.1.2",          // Select dropdowns
"@swimlane/ngx-datatable": "^22.0.0",       // Data tables
"highcharts": "11.4.7",                     // Charts and graphs
"ngx-quill": "^28.0.1",                     // Rich text editor
"@xterm/xterm": "^5.5.0"                    // Terminal component
```

### Styling Architecture
```scss
// SCSS structure
src/styles/
├── app/
│   ├── _variables.scss      # Custom variables
│   ├── _mixins.scss         # Utility mixins
│   └── _components.scss     # Component styles
├── styles.scss              # Global styles
└── themes/                  # Theme configurations
```

### Component Styling Patterns
```typescript
@Component({
    selector: 'app-example',
    styleUrls: ['./example.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ExampleComponent {
    // Component implementation
}
```

### Styling Best Practices
- **Bootstrap Classes**: Use Bootstrap 5.3 classes for consistent styling
- **Flexbox Utilities**: Use Bootstrap flexbox utilities for layouts
- **Custom Variables**: Extend and override Bootstrap variables in `custom-variables.scss`
- **Shared Styles**: Check existing shared styles in `styles/app/` before creating new ones

### Styling Guidelines
```scss
// ✅ Do: Use Bootstrap classes
<div class="d-flex justify-content-between align-items-center">
    <span class="text-primary">Content</span>
</div>

// ✅ Do: Use CSS classes for styling
.custom-button {
    background-color: var(--primary-color);
    border-radius: 0.375rem;
}

// ❌ Don't: Use inline styles
<div [style]="'color: red; margin: 10px'">Content</div>

// ✅ Do: Use class bindings
<div [class.active]="isActive()" [class.disabled]="!isEnabled()">Content</div>
```

### Theme Customization
```scss
// custom-variables.scss - Preferred way to customize theme
$primary: #007bff;
$secondary: #6c757d;
$success: #28a745;

// Override Bootstrap variables before importing
@import "~bootstrap/scss/bootstrap";
```

## Build Configuration

### Angular Configuration
```json
// angular.json key configurations
{
    "build": {
        "builder": "@angular/build:application",
        "options": {
            "outputPath": { "base": "dist", "browser": "" },
            "index": "src/index.html",
            "browser": "src/main.ts",
            "polyfills": ["zone.js", "@angular/localize/init"],
            "tsConfig": "tsconfig.app.json",
            "inlineStyleLanguage": "scss",
            "assets": ["src/favicon.ico", "src/assets"],
            "styles": [
                "src/styles.scss",
                "./node_modules/quill/dist/quill.core.css",
                "./node_modules/@xterm/xterm/css/xterm.css"
            ],
            "allowedCommonJsDependencies": [
                "highcharts", "@xterm/addon-fit", "@xterm/xterm"
            ]
        }
    }
}
```

### Environment Configuration
```typescript
// environment.ts
export const environment: EnvironmentModel = {
    production: false,
    server: '/api/'
};

// environment.prod.ts
export const environment: EnvironmentModel = {
    production: true,
    server: '/api/'
};
```

### Build Scripts
```json
// package.json scripts
{
    "scripts": {
        "start": "ng serve --ssl --ssl-key .\\certs\\localhost.key --ssl-cert .\\certs\\localhost.crt",
        "build": "npm run lint && npm run test-ci && ng build",
        "build-prod": "npm run lint && npm run test-ci && ng build --configuration=production",
        "test": "ng test",
        "test-ci": "ng test --configuration=ci",
        "lint": "ng lint"
    }
}
```

### Development vs Production
- **Development**: Source maps, verbose logging, hot reload
- **Production**: Minification, tree shaking, optimized bundles
- **SSL**: HTTPS support for local development
- **Proxy**: API proxy configuration for development

## Testing Guidelines

### Unit Testing Best Practices
- **Coverage Requirements**: Cover implementation with unit tests where it makes sense
- **Fixture Testing**: Use fixture instead of accessing component API directly, except for specific helper functions
- **Observable Testing**: Use jasmine marbles to test observables
- **TestBed Setup**: Always use TestBed to setup tests
- **Test Identification**: Use custom attribute `data-testid` to identify elements that cannot be easily identified in unit tests

### Test Scripts
```json
{
  "scripts": {
    "test": "ng test",           // Runs tests on full Chrome instance for debugging
    "test-ci": "ng test --configuration=ci"  // Runs tests on headless Chrome (build pipeline)
  }
}
```

### Testing Requirements
- **PR Completion**: PRs won't be completed until unit testing is at least discussed
- **Build Validation**: Ensure tests pass before submitting PR using `npm run test-ci`
- **Coverage Reports**: Test coverage accessible at `ClientApp/coverage/{browser}/index.html`

### Grid Testing Patterns
```typescript
// Import required modules for grid testing
await TestBed.configureTestingModule({
    providers: [
        provideMock(NotificationsService),
        provideMock(ModalService),
        provideMock(UserContextService),
    ],
    declarations: [NotificationsListComponent],
    imports: [
        NgxDatatableModule, // Required for grid testing
        SharedModule
    ]
}).compileComponents();

// Test actual generated rows
it('should have the same amount of rows as data', () => {
    const rows = dataTable.querySelectorAll('datatable-row-wrapper');
    expect(rows.length).toEqual(data.length);
});

// Get component instances for testing
beforeEach(async () => {
    dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
    dataTable = dataTableDebugElement.nativeElement;
    fixture.detectChanges();
});
```

## Forms and UI Components

### Form Development Guidelines
- **Reactive Forms**: Always use Reactive Forms API to interact with forms and HTML elements
- **Validation**: Validate inputs using reactive forms validators
- **State Management**: Handle input changes using FormControl valueChanges
- **Control State**: Toggle input state using enable/disable functionality of FormControl
- **Typed Forms**: Use typed forms and construct with all known values and state

### Form Anti-Patterns
```typescript
// ❌ Don't: Direct HTML interaction
<input [(ngModel)]="value" (change)="onChange()">

// ✅ Do: Reactive Forms
<input [formControl]="nameControl">
```

### Dropdown Components (ng-select)
```typescript
// Use ng-select for all dropdowns
<ng-select [formControl]="userControl"
           [items]="users"
           bindLabel="name"
           bindValue="id"
           [typeahead]="userTypeahead$">
</ng-select>

// Handle changes via FormControl
this.userControl.valueChanges.subscribe(value => {
    // Handle selection change
});

// Exception: Use (clear) event for typeahead when backspace doesn't trigger valueChanges
<ng-select (clear)="resetInput()">
```

### Modal Service Usage
```typescript
// Confirmation dialogs
const modalRef = this.modalService.openConfirmationDialog({
    content: 'Are you sure?',
    title: 'Confirm Action',
    confirmButtonText: 'Yes, Continue'
});

// Delete confirmations
const modalRef = this.modalService.openDeleteConfirmationDialog(
    'Delete Item',
    'This action cannot be undone'
);

// Modal components
const modalRef = this.modalService.openModalComponent(MyComponent, {
    size: 'lg',
    backdrop: 'static'
});

// Submit buttons in modals
<app-btn-submit [btnClasses]="'ms-2 btn btn-primary'"
                [disabled]="formGroup.invalid"
                (submitClickEvent)="submit()">
    {{acceptText}}
</app-btn-submit>
```

## Grid Components

### BaseListComponent Pattern
```typescript
// Extend BaseListComponent for server-side paging, sorting, and filtering
export class NotificationsListComponent extends BaseListComponent<Notification> implements OnInit {
    constructor(
        private notificationsService: NotificationsService,
        private userContextService: UserContextService
    ) {
        super(menuService);
        this.pagination = new NotificationsListRequest();
    }

    ngOnInit() {
        const columns: TableColumn[] = [
            {
                name: 'Host Name',
                prop: 'hostname',
                headerTemplate: this.headerTemplate,
                sortable: true,
                resizeable: true,
                canAutoResize: true
            }
        ];

        super.initialize(
            this.agentService.getAgentList.bind(this.agentService, this.userContextService.currentUser.organizationId),
            columns
        );
    }
}
```

### Grid Layout Structure
```html
<!-- Content heading -->
<div class="content-heading">
    Grid Title
</div>

<!-- Subheading (always include for spacing) -->
<div class="content-sub-heading">
    Optional subtitle
</div>

<!-- Action buttons -->
<div class="action-buttons">
    <button *ngIf="permissionService.canCreate()"
            class="btn btn-primary"
            (click)="create()">
        Add Item
    </button>
    <app-back [buttonText]="'Back to List'"></app-back>
</div>
```

### Grid Filtering
```typescript
// Search term filter
<app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)"
                     [placeholder]="column.name"
                     [dataItemName]="column.prop">
</app-auto-search-box>

// Custom filter popup
export class LogListFilter {
    constructor() {
        this.fromDate = new Date();
        this.fromDate.setHours(this.fromDate.getHours() - 12);
    }
    fromDate: Date;
    toDate = new Date();
    userId?: number;
    organizationId?: number;
    viewHttpRequests: LogListFilterRequestView = LogListFilterRequestView.ViewErrorsOnly;
    viewEntityChanges = false;
}
```

## Template Performance

### Template Optimization Guidelines
- **Avoid Function Calls**: Don't call functions directly in template expressions
- **Use Signals**: Prefer signals for computed values
- **Pure Pipes**: Use pure pipes for data transformation
- **Store Computed Values**: Store function results in component fields

### Performance Anti-Patterns
```typescript
// ❌ Bad: Function calls in template
@Component({
    template: '<i [class]="getIconClass()"></i>'
})
export class BadComponent {
    getIconClass(): string {
        return this.icon + (this.show ? ' enabled' : ' disabled');
    }
}

// ✅ Good: Computed property
@Component({
    template: '<i [class]="iconClass"></i>'
})
export class GoodComponent implements OnInit {
    iconClass = '';

    ngOnInit(): void {
        this.iconClass = this.icon + (this.show ? ' enabled' : ' disabled');
    }
}

// ✅ Better: Using signals
@Component({
    template: '<i [class]="iconClass()"></i>'
})
export class BetterComponent {
    iconClass = computed(() =>
        this.icon() + (this.show() ? ' enabled' : ' disabled')
    );
}
```

### Type Safety in Templates
```typescript
// Use toItem() method for type safety in BaseListComponent
<ng-template #someTableTemplate let-row="row">
    <ng-container *ngIf="toItem(row); let row">
        {{ row.someProperty }}  <!-- Now has proper typing -->
    </ng-container>
</ng-template>
```

## Build and Development

### Build Process
```bash
# Full build process (includes linting, testing, and compilation)
npm run build

# This is what the build pipeline executes
# Must pass locally before submitting PR
```

### Development Requirements
- **Build Validation**: Run `npm run build` locally before PR submission
- **No Errors/Warnings**: Build must pass with no errors or warnings
- **Test Coverage**: All tests must pass in CI environment

## Best Practices

### Code Organization
- **Feature Modules**: Organize by business domain
- **Shared Components**: Reusable UI components
- **Barrel Exports**: Use index.ts for clean imports
- **Path Mapping**: Use @app alias for imports

### Performance Optimization
- **OnPush Strategy**: Minimize change detection cycles
- **Lazy Loading**: Load features on demand
- **TrackBy Functions**: Optimize *ngFor performance
- **Signal-based**: Use signals for reactive programming

### Development Guidelines
- **TypeScript Strict Mode**: Enable strict type checking
- **ESLint**: Code quality and consistency
- **Prettier**: Code formatting
- **Testing**: Unit tests with Jasmine/Karma
